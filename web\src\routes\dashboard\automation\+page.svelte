<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Alert from '$lib/components/ui/alert';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import SEO from '$components/shared/SEO.svelte';
  import {
    Play,
    Search,
    FileText,
    Plus,
    ExternalLink,
    Clock,
    CheckCircle,
    XCircle,
    StopCircle,
    Terminal,
  } from 'lucide-svelte';

  import AutomationRunSheet from '$lib/../components/automation/AutomationRunSheet.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { formatDistance } from 'date-fns';
  import { writable } from 'svelte/store';
  import * as Dialog from '$lib/components/ui/dialog';

  const data = $props();
  let profiles = $state(data.profiles || []);
  // Convert automationRuns to a proper Svelte store
  const automationRuns = writable(data.automationRuns || []);

  // Sheet state
  let selectedRun = $state(null);
  let isSheetOpen = $state(false);

  // State for new automation run
  let selectedProfileId = $state('');
  let selectedKeywords = $state('');
  let selectedLocation = $state('');
  let createDialogOpen = $state(false);
  let isCreatingRun = $state(false);

  // Helper function to format distance to now
  function formatDistanceToNow(date: Date | string): string {
    if (!date) return '';
    return formatDistance(new Date(date), new Date(), { addSuffix: false });
  }

  // Function to create a new automation run
  async function createAutomationRun() {
    if (!selectedProfileId) {
      toast.error('Please select a profile');
      return;
    }

    isCreatingRun = true;

    try {
      // Call the API to create a new automation run
      const response = await fetch('/api/automation/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileId: selectedProfileId,
          keywords: selectedKeywords,
          location: selectedLocation,
        }),
      });

      if (response.ok) {
        const newRun = await response.json();
        toast.success('Automation run created successfully');
        toast.info('The automation system is processing your request');
        createDialogOpen = false;
        resetForm();

        // Redirect to the automation run detail page
        window.location.href = `/dashboard/automation/${newRun.id}`;
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create automation run');
      }
    } catch (error) {
      console.error('Error creating automation run:', error);
      toast.error('An error occurred while creating the automation run');
    } finally {
      isCreatingRun = false;
    }
  }

  // Function to reset the form
  function resetForm() {
    selectedProfileId = '';
    selectedKeywords = '';
    selectedLocation = '';
  }

  // Function to get status badge variant
  function getStatusBadgeVariant(
    status: string
  ): 'default' | 'destructive' | 'outline' | 'secondary' {
    switch (status) {
      case 'running':
        return 'default';
      case 'completed':
        return 'secondary'; // Changed from 'success' to 'secondary' to match available variants
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'outline'; // Changed from 'warning' to 'outline' to match available variants
      default:
        return 'secondary';
    }
  }

  // Function to get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'running':
        return Play;
      case 'completed':
        return CheckCircle;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      case 'pending':
        return Clock;
      default:
        return Clock;
    }
  }

  // Function to calculate progress percentage
  function calculateProgress(run: any) {
    if (!run) return 0;
    if (run.status === 'completed') return 100;
    if (run.status === 'failed' || run.status === 'stopped') return run.progress || 0;
    return run.progress || 0;
  }

  // Function to handle refreshing a run
  function handleRunRefresh(updatedRun: any) {
    automationRuns.update((runs) =>
      runs.map((run: any) => (run.id === updatedRun.id ? updatedRun : run))
    );
  }
</script>

<SEO
  title="Job Automation | Hirli"
  description="Automate your job search and application process with Hirli's intelligent automation tools."
  keywords="job automation, automated job search, job application automation, resume matching, career automation, job search tools" />

<div class="flex w-full flex-col">
  <div class="flex flex-col gap-8 p-4">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold">Automation</h1>
        <p class="text-muted-foreground text-sm">
          Automate your job search and application process
        </p>
      </div>
      <Button variant="default" onclick={() => (createDialogOpen = true)}>
        <Play class="mr-2 h-4 w-4" />
        New Automation Run
      </Button>
    </div>
  </div>

  <Tabs.Root value="runs">
    <Tabs.List class="flex flex-row gap-2 divide-x rounded-none px-2">
      <Tabs.Trigger value="runs" class="flex-1">Automation Runs</Tabs.Trigger>
      <Tabs.Trigger value="profiles" class="flex-1">Available Profiles</Tabs.Trigger>
    </Tabs.List>

    <Tabs.Content value="runs" class="mt-0 p-4">
      <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div class="flex flex-wrap items-center gap-2">
          <Button variant="outline" size="sm" class="gap-1">
            <Clock class="h-4 w-4" />
            All
          </Button>
          <Button variant="outline" size="sm" class="gap-1">
            <Play class="h-4 w-4" />
            Running
          </Button>
          <Button variant="outline" size="sm" class="gap-1">
            <CheckCircle class="h-4 w-4" />
            Completed
          </Button>
          <Button variant="outline" size="sm" class="gap-1">
            <StopCircle class="h-4 w-4" />
            Stopped
          </Button>
        </div>
        <div class="flex items-center gap-2">
          <Input placeholder="Search..." class="w-[200px]" />
          <Button variant="outline" size="icon">
            <Search class="h-4 w-4" />
          </Button>
        </div>
      </div>
      {#if $automationRuns.length === 0}
        <div
          class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
          <Search class="mb-4 h-12 w-12 text-gray-400" />
          <h3 class="text-xl font-semibold text-gray-300">No automation runs yet</h3>
          <p class="mt-2 text-gray-400">
            Create your first automation run to start searching for jobs
          </p>
          <Button variant="default" onclick={() => (createDialogOpen = true)} class="mt-4">
            <Play class="mr-2 h-4 w-4" />
            New Automation Run
          </Button>
        </div>
      {:else}
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {#each $automationRuns as run (run.id)}
            <Card.Root class="overflow-hidden">
              <Card.Header class="p-4 pb-0">
                <div class="flex items-center justify-between">
                  <Card.Title>
                    {#if run.profile}
                      {getProfileData(run.profile).fullName || 'Unnamed Profile'}
                    {:else}
                      Automation Run
                    {/if}
                  </Card.Title>
                  <Badge variant={getStatusBadgeVariant(run.status)} class="ml-2">
                    {#if getStatusIcon(run.status)}
                      {@const Icon = getStatusIcon(run.status)}
                      <Icon class="mr-1 h-3 w-3" />
                    {/if}
                    {run.status.charAt(0).toUpperCase() + run.status.slice(1)}
                  </Badge>
                </div>
                <Card.Description>
                  {#if run.createdAt}
                    Started {formatDistanceToNow(new Date(run.createdAt))} ago
                  {/if}
                </Card.Description>
              </Card.Header>

              <Card.Content class="p-4">
                <div class="mb-4">
                  <div class="mb-1 text-sm font-medium text-gray-400">Progress</div>
                  <Progress value={calculateProgress(run)} max={100} />
                  <div class="mt-1 text-right text-xs text-gray-400">
                    {calculateProgress(run)}% Complete
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div class="font-medium text-gray-400">Keywords</div>
                    <div class="truncate">{run.keywords || 'None'}</div>
                  </div>
                  <div>
                    <div class="font-medium text-gray-400">Location</div>
                    <div class="truncate">{run.location || 'None'}</div>
                  </div>
                </div>

                <div class="mt-4">
                  <div class="font-medium text-gray-400">Jobs Found</div>
                  <div class="flex items-center gap-2">
                    <span class="text-lg font-semibold">{run.jobs?.length || 0}</span>
                    {#if run.status === 'running' || run.status === 'pending'}
                      <span class="text-xs text-gray-400">(in progress)</span>
                    {/if}
                  </div>
                </div>
              </Card.Content>

              <Card.Footer class="border-t p-4">
                <div class="flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onclick={() => {
                      selectedRun = run;
                      isSheetOpen = true;
                    }}>
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onclick={() => (window.location.href = `/dashboard/automation/${run.id}`)}>
                    <ExternalLink class="mr-2 h-4 w-4" />
                    Full View
                  </Button>
                </div>
              </Card.Footer>
            </Card.Root>
          {/each}
        </div>
      {/if}
    </Tabs.Content>

    <Tabs.Content value="profiles" class="mt-0 p-4">
      <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div class="flex flex-wrap items-center gap-2">
          <Button variant="outline" size="sm" class="gap-1">
            <FileText class="h-4 w-4" />
            All Profiles
          </Button>
          <Button
            variant="default"
            size="sm"
            onclick={() => (window.location.href = '/dashboard/settings/profile')}
            class="gap-1">
            <Plus class="h-4 w-4" />
            Create Profile
          </Button>
        </div>
        <div class="flex items-center gap-2">
          <Input placeholder="Search profiles..." class="w-[200px]" />
          <Button variant="outline" size="icon">
            <Search class="h-4 w-4" />
          </Button>
        </div>
      </div>
      {#if profiles.length === 0}
        <div
          class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
          <FileText class="mb-4 h-12 w-12 text-gray-400" />
          <h3 class="text-xl font-semibold text-gray-300">No profiles available</h3>
          <p class="mt-2 text-gray-400">Create a profile first to use automation</p>
          <Button
            variant="default"
            onclick={() => (window.location.href = '/dashboard/settings/profile')}
            class="mt-4">
            Create Profile
          </Button>
        </div>
      {:else}
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {#each profiles as profile (profile.id)}
            <Card.Root>
              <Card.Header class="p-6">
                <Card.Title>
                  <a href={`/dashboard/automation/profile/${profile.id}`} class="hover:underline">
                    {getProfileData(profile).fullName || 'Unnamed Profile'}
                  </a>
                </Card.Title>
                <Card.Description>
                  {getProfileData(profile).title || 'No title specified'}
                </Card.Description>
              </Card.Header>
              <Card.Content class="p-6 pt-0">
                <div class="mb-4">
                  <div class="text-sm font-medium text-gray-400">Resume</div>
                  <div>
                    {#if profile.documents && profile.documents.length > 0}
                      <Badge variant="outline" class="mt-1">
                        <FileText class="mr-1 h-3 w-3" />
                        {profile.documents.length}
                        {profile.documents.length === 1 ? 'resume' : 'resumes'} available
                      </Badge>
                    {:else}
                      <Badge variant="outline" class="mt-1 text-gray-400">No resume</Badge>
                    {/if}
                  </div>
                </div>
              </Card.Content>
              <Card.Footer class="p-6 pt-0">
                <Button
                  variant="default"
                  class="w-full"
                  onclick={() => {
                    selectedProfileId = profile.id;
                    createDialogOpen = true;
                  }}>
                  <Play class="mr-2 h-4 w-4" />
                  Run Automation
                </Button>
              </Card.Footer>
            </Card.Root>
          {/each}
        </div>
      {/if}
    </Tabs.Content>
  </Tabs.Root>
</div>

<!-- Create Automation Run Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
  <Dialog.Overlay />
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>Create Automation Run</Dialog.Title>
      <Dialog.Description>
        Start a new automation run to search for jobs based on a profile.
      </Dialog.Description>
    </Dialog.Header>

    <div class="grid gap-4 py-4">
      <div class="grid gap-2">
        <div class="flex items-center justify-between">
          <label for="profile" class="text-sm font-medium">Profile</label>
          <Button
            variant="ghost"
            size="sm"
            onclick={() => (window.location.href = '/dashboard/settings/profile')}
            class="text-xs text-blue-500 hover:text-blue-400">
            <Plus class="mr-1 h-3 w-3" />
            Create New
          </Button>
        </div>
        <select
          id="profile"
          bind:value={selectedProfileId}
          class="border-input bg-background w-full rounded-md border px-3 py-2">
          <option value="">Select a profile</option>
          {#each profiles as profile}
            <option value={profile.id}>
              {getProfileData(profile).fullName || 'Unnamed Profile'}
            </option>
          {/each}
        </select>
      </div>

      <div class="grid gap-2">
        <label for="keywords" class="text-sm font-medium">Keywords (optional)</label>
        <Input
          id="keywords"
          bind:value={selectedKeywords}
          placeholder="e.g. Software Engineer, Developer" />
      </div>

      <div class="grid gap-2">
        <label for="location" class="text-sm font-medium">Location (optional)</label>
        <Input id="location" bind:value={selectedLocation} placeholder="e.g. New York, Remote" />
      </div>
    </div>

    <Alert.Root>
      <Terminal class="size-4" />
      <Alert.Title>Access Restricted</Alert.Title>
      <Alert.Description>
        Cancel
        <Button
          variant="link"
          class="h-auto p-0"
          onclick={createAutomationRun}
          disabled={!selectedProfileId || isCreatingRun}>
          {#if isCreatingRun}
            Creating...
          {:else}
            Start Automation
          {/if}</Button>
      </Alert.Description>
    </Alert.Root>
  </Dialog.Content>
</Dialog.Root>

<!-- Automation Run Sheet -->
{#if selectedRun}
  <AutomationRunSheet
    bind:open={isSheetOpen}
    automationRun={selectedRun}
    onClose={() => {
      selectedRun = null;
    }}
    onRefresh={handleRunRefresh}
    onStop={() => {
      automationRuns.update((runs) =>
        runs.map((run: any) => {
          if (run.id === selectedRun.id) {
            return { ...run, status: 'stopped' };
          }
          return run;
        })
      );
    }} />
{/if}
