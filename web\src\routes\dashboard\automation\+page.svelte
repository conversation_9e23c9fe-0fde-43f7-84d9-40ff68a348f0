<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Alert from '$lib/components/ui/alert';
  import SEO from '$components/shared/SEO.svelte';
  import { Plus, Terminal, Play } from 'lucide-svelte';

  import AutomationRunSheet from '$lib/../components/automation/AutomationRunSheet.svelte';
  import AutomationRunsTab from '$components/automation/AutomationRunsTab.svelte';
  import ProfilesTab from '$components/automation/ProfilesTab.svelte';
  import FeatureGuard from '$lib/components/features/FeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { writable } from 'svelte/store';
  import * as Dialog from '$lib/components/ui/dialog';

  const { data } = $props();

  let profiles = $state(data.profiles || []);
  // Convert automationRuns to a proper Svelte store
  const automationRuns = writable(data.automationRuns || []);

  // Sheet state
  let selectedRun = $state(null);
  let isSheetOpen = $state(false);

  // State for new automation run
  let selectedProfileId = $state('');
  let selectedKeywords = $state('');
  let selectedLocation = $state('');
  let createDialogOpen = $state(false);
  let isCreatingRun = $state(false);

  // Function to create a new automation run
  async function createAutomationRun() {
    if (!selectedProfileId) {
      toast.error('Please select a profile');
      return;
    }

    isCreatingRun = true;

    try {
      // Call the API to create a new automation run
      const response = await fetch('/api/automation/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileId: selectedProfileId,
          keywords: selectedKeywords,
          location: selectedLocation,
        }),
      });

      if (response.ok) {
        const newRun = await response.json();
        toast.success('Automation run created successfully');
        toast.info('The automation system is processing your request');
        createDialogOpen = false;
        resetForm();

        // Redirect to the automation run detail page
        window.location.href = `/dashboard/automation/${newRun.id}`;
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create automation run');
      }
    } catch (error) {
      console.error('Error creating automation run:', error);
      toast.error('An error occurred while creating the automation run');
    } finally {
      isCreatingRun = false;
    }
  }

  // Function to reset the form
  function resetForm() {
    selectedProfileId = '';
    selectedKeywords = '';
    selectedLocation = '';
  }

  // Function to handle refreshing a run
  function handleRunRefresh(updatedRun: any) {
    automationRuns.update((runs) =>
      runs.map((run: any) => (run.id === updatedRun.id ? updatedRun : run))
    );
  }
</script>

<SEO
  title="Job Automation | Hirli"
  description="Automate your job search and application process with Hirli's intelligent automation tools."
  keywords="job automation, automated job search, job application automation, resume matching, career automation, job search tools" />

<div class="flex w-full flex-col">
  <div class="flex flex-col gap-8 p-4">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold">Automation</h1>
        <p class="text-muted-foreground text-sm">
          Automate your job search and application process
        </p>
      </div>
      <Button variant="default" onclick={() => (createDialogOpen = true)}>
        <Play class="mr-2 h-4 w-4" />
        New Automation Run
      </Button>
    </div>
  </div>

  <Tabs.Root value="runs">
    <Tabs.List class="flex flex-row gap-2 divide-x rounded-none px-2">
      <Tabs.Trigger value="runs" class="flex-1">Automation Runs</Tabs.Trigger>
      <Tabs.Trigger value="profiles" class="flex-1">Available Profiles</Tabs.Trigger>
    </Tabs.List>

    <Tabs.Content value="runs" class="mt-0 p-4">
      <AutomationRunsTab
        userData={data.user}
        {automationRuns}
        onRunSelect={(run) => {
          selectedRun = run;
          isSheetOpen = true;
        }}
        onCreateRun={() => (createDialogOpen = true)} />
    </Tabs.Content>

    <Tabs.Content value="profiles" class="mt-0 p-4">
      <ProfilesTab
        userData={data.user}
        {profiles}
        onProfileSelect={(profileId) => {
          selectedProfileId = profileId;
          createDialogOpen = true;
        }} />
    </Tabs.Content>
  </Tabs.Root>
</div>

<!-- Create Automation Run Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
  <Dialog.Overlay />
  <Dialog.Content>
    <FeatureGuard
      userData={data.user}
      featureId="automation"
      limitId="automation_runs_per_month"
      showUpgradeButton={true}
      upgradeButtonText="Upgrade for Automation"
      limitReachedMessage="You've reached your monthly limit for automation runs"
      notIncludedMessage="Automation is not included in your current plan">
      <Dialog.Header>
        <Dialog.Title>Create Automation Run</Dialog.Title>
        <Dialog.Description>
          Start a new automation run to search for jobs based on a profile.
        </Dialog.Description>
      </Dialog.Header>

      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <div class="flex items-center justify-between">
            <label for="profile" class="text-sm font-medium">Profile</label>
            <Button
              variant="ghost"
              size="sm"
              onclick={() => (window.location.href = '/dashboard/settings/profile')}
              class="text-xs text-blue-500 hover:text-blue-400">
              <Plus class="mr-1 h-3 w-3" />
              Create New
            </Button>
          </div>
          <select
            id="profile"
            bind:value={selectedProfileId}
            class="border-input bg-background w-full rounded-md border px-3 py-2">
            <option value="">Select a profile</option>
            {#each profiles as profile}
              <option value={profile.id}>
                {getProfileData(profile).fullName || 'Unnamed Profile'}
              </option>
            {/each}
          </select>
        </div>

        <div class="grid gap-2">
          <label for="keywords" class="text-sm font-medium">Keywords (optional)</label>
          <Input
            id="keywords"
            bind:value={selectedKeywords}
            placeholder="e.g. Software Engineer, Developer" />
        </div>

        <div class="grid gap-2">
          <label for="location" class="text-sm font-medium">Location (optional)</label>
          <Input id="location" bind:value={selectedLocation} placeholder="e.g. New York, Remote" />
        </div>
      </div>

      <Alert.Root>
        <Terminal class="size-4" />
        <Alert.Title>Access Restricted</Alert.Title>
        <Alert.Description>
          Cancel
          <Button
            variant="link"
            class="h-auto p-0"
            onclick={createAutomationRun}
            disabled={!selectedProfileId || isCreatingRun}>
            {#if isCreatingRun}
              Creating...
            {:else}
              Start Automation
            {/if}</Button>
        </Alert.Description>
      </Alert.Root>
    </FeatureGuard>
  </Dialog.Content>
</Dialog.Root>

<!-- Automation Run Sheet -->
{#if selectedRun}
  <AutomationRunSheet
    bind:open={isSheetOpen}
    automationRun={selectedRun}
    onClose={() => {
      selectedRun = null;
    }}
    onRefresh={handleRunRefresh}
    onStop={() => {
      automationRuns.update((runs) =>
        runs.map((run: any) => {
          if (run.id === selectedRun.id) {
            return { ...run, status: 'stopped' };
          }
          return run;
        })
      );
    }} />
{/if}
