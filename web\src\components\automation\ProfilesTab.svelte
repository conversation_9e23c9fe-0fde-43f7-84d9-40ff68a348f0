<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { Search, Play, Plus, FileText } from 'lucide-svelte';

  const { userData, profiles, onProfileSelect } = $props<{
    userData: any;
    profiles: any[];
    onProfileSelect: (profileId: string) => void;
  }>();

  // Search state
  let profileSearchQuery = $state('');

  // Filtered profiles
  const filteredProfiles = $derived(() => {
    return profiles.filter((profile) => {
      // Search filter
      if (profileSearchQuery.trim()) {
        const query = profileSearchQuery.toLowerCase();
        const profileData = getProfileData(profile);
        const name = profileData.fullName || profile.name || '';
        const title = profileData.title || '';

        return name.toLowerCase().includes(query) || title.toLowerCase().includes(query);
      }

      return true;
    });
  });
</script>

<div class="mb-6 flex flex-wrap items-center justify-between gap-4">
  <div class="flex flex-wrap items-center gap-2">
    <Button
      variant="default"
      size="sm"
      onclick={() => (window.location.href = '/dashboard/settings/profile')}
      class="gap-1">
      <Plus class="h-4 w-4" />
      Manage Profiles
    </Button>
  </div>
  <div class="flex items-center gap-2">
    <Input placeholder="Search profiles..." class="w-[200px]" bind:value={profileSearchQuery} />
  </div>
</div>

{#if profiles.length === 0}
  <div
    class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
    <FileText class="mb-4 h-12 w-12 text-gray-400" />
    <h3 class="text-xl font-semibold text-gray-300">No profiles available</h3>
    <p class="mt-2 text-gray-400">Create a profile in Settings to start using automation</p>
    <Button
      variant="default"
      onclick={() => (window.location.href = '/dashboard/settings/profile')}
      class="mt-4">
      Go to Profile Settings
    </Button>
  </div>
{:else if filteredProfiles().length === 0}
  <div
    class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
    <Search class="mb-4 h-12 w-12 text-gray-400" />
    <h3 class="text-xl font-semibold text-gray-300">No profiles match your search</h3>
    <p class="mt-2 text-gray-400">Try adjusting your search criteria</p>
  </div>
{:else}
  <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
    {#each filteredProfiles() as profile (profile.id)}
      <Card.Root>
        <Card.Header class="p-6">
          <Card.Title>
            <a href={`/dashboard/automation/profile/${profile.id}`} class="hover:underline">
              {getProfileData(profile).fullName || 'Unnamed Profile'}
            </a>
          </Card.Title>
          <Card.Description>
            {getProfileData(profile).title || 'No title specified'}
          </Card.Description>
        </Card.Header>
        <Card.Content class="p-6 pt-0">
          <div class="mb-4">
            <div class="text-sm font-medium text-gray-400">Resume</div>
            <div>
              {#if profile.documents && profile.documents.length > 0}
                <Badge variant="outline" class="mt-1">
                  <FileText class="mr-1 h-3 w-3" />
                  {profile.documents.length}
                  {profile.documents.length === 1 ? 'resume' : 'resumes'} available
                </Badge>
              {:else}
                <Badge variant="outline" class="mt-1 text-gray-400">No resume</Badge>
              {/if}
            </div>
          </div>
        </Card.Content>
        <Card.Footer class="p-6 pt-0">
          <FeatureGuard
            {userData}
            featureId="automation"
            limitId="automation_runs_per_month"
            showUpgradePrompt={true}
            fallbackMessage="Automation features are not available in your current plan">
            <Button variant="default" class="w-full" onclick={() => onProfileSelect(profile.id)}>
              <Play class="mr-2 h-4 w-4" />
              Run Automation
            </Button>
          </FeatureGuard>
        </Card.Footer>
      </Card.Root>
    {/each}
  </div>
{/if}
