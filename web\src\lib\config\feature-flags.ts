/**
 * Central Feature Flag Configuration
 * This file controls all feature access across the application
 */

export interface FeatureConfig {
  enabled: boolean;
  bypassInDevelopment?: boolean;
  description?: string;
}

/**
 * Global feature configuration
 * Set enabled: false to completely disable a feature
 * Set bypassInDevelopment: true to bypass limits in development
 */
export const FEATURE_FLAGS: Record<string, FeatureConfig> = {
  // Core features
  dashboard: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Main dashboard access',
  },

  profile: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'User profile management',
  },

  // Automation features
  automation: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Job automation and application features',
  },

  // Resume features
  resume_scanner: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Resume scanning and analysis',
  },

  ats_optimization: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'ATS optimization features',
  },

  // AI features
  cover_letter_generator: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'AI-powered cover letter generation',
  },

  ai_matching: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'AI job matching',
  },

  // Analytics features
  analytics: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Job market analytics and insights',
  },

  // Team features
  team_collaboration: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Team collaboration features',
  },

  // Integration features
  linkedin_integration: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'LinkedIn integration',
  },

  // Communication features
  email_support: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Email support access',
  },

  // Advanced features
  api_access: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'API access for integrations',
  },

  custom_branding: {
    enabled: true,
    bypassInDevelopment: true,
    description: 'Custom branding options',
  },
};

/**
 * Environment-based feature control
 */
export const ENVIRONMENT_CONFIG = {
  // Disable all feature limits in development
  DISABLE_ALL_LIMITS:
    process.env.NODE_ENV === 'development' || process.env.VITE_DISABLE_FEATURE_LIMITS === 'true',

  // Disable specific features via environment
  DISABLED_FEATURES: (process.env.VITE_DISABLED_FEATURES || '').split(',').filter(Boolean),

  // Enable all features for testing
  ENABLE_ALL_FEATURES: process.env.VITE_ENABLE_ALL_FEATURES === 'true',

  // Development mode bypass
  DEVELOPMENT_BYPASS:
    process.env.NODE_ENV === 'development' && process.env.VITE_STRICT_FEATURE_CHECKS !== 'true',
};

/**
 * Check if a feature is enabled
 */
export function isFeatureEnabled(featureId: string): boolean {
  // Environment override - disable all features
  if (ENVIRONMENT_CONFIG.DISABLED_FEATURES.includes('*')) {
    return false;
  }

  // Environment override - enable all features
  if (ENVIRONMENT_CONFIG.ENABLE_ALL_FEATURES) {
    return true;
  }

  // Environment override - disable specific feature
  if (ENVIRONMENT_CONFIG.DISABLED_FEATURES.includes(featureId)) {
    return false;
  }

  // Check feature flag configuration
  const featureConfig = FEATURE_FLAGS[featureId];
  if (!featureConfig) {
    console.warn(`Feature '${featureId}' not found in FEATURE_FLAGS`);
    return false;
  }

  return featureConfig.enabled;
}

/**
 * Check if feature limits should be bypassed
 */
export function shouldBypassLimits(featureId: string): boolean {
  // Global environment bypass
  if (ENVIRONMENT_CONFIG.DISABLE_ALL_LIMITS) {
    return true;
  }

  // Development bypass for specific feature
  const featureConfig = FEATURE_FLAGS[featureId];
  if (featureConfig?.bypassInDevelopment && ENVIRONMENT_CONFIG.DEVELOPMENT_BYPASS) {
    return true;
  }

  return false;
}

/**
 * Get all enabled features
 */
export function getEnabledFeatures(): string[] {
  return Object.entries(FEATURE_FLAGS)
    .filter(([featureId]) => isFeatureEnabled(featureId))
    .map(([featureId]) => featureId);
}

/**
 * Get feature configuration
 */
export function getFeatureConfig(featureId: string): FeatureConfig | null {
  return FEATURE_FLAGS[featureId] || null;
}

/**
 * Runtime feature toggle (for admin/development use)
 */
export function toggleFeature(featureId: string, enabled: boolean): void {
  if (FEATURE_FLAGS[featureId]) {
    FEATURE_FLAGS[featureId].enabled = enabled;
    console.log(`Feature '${featureId}' ${enabled ? 'enabled' : 'disabled'}`);
  }
}

/**
 * Development helper to list all features
 */
export function listAllFeatures(): void {
  if (process.env.NODE_ENV === 'development') {
    console.table(
      Object.entries(FEATURE_FLAGS).map(([id, config]) => ({
        Feature: id,
        Enabled: config.enabled,
        'Bypass in Dev': config.bypassInDevelopment,
        Description: config.description,
      }))
    );
  }
}
