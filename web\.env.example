# Feature Control Environment Variables

# Disable all feature limits (useful for development/testing)
VITE_DISABLE_FEATURE_LIMITS=true

# Disable specific features (comma-separated list)
# VITE_DISABLED_FEATURES=automation,ai_matching,analytics

# Enable all features regardless of subscription (testing mode)
# VITE_ENABLE_ALL_FEATURES=true

# Strict feature checks even in development
# VITE_STRICT_FEATURE_CHECKS=true

# Development mode (automatically detected, but can be overridden)
NODE_ENV=development
